/* =============================================
   🏛️ BAKASANA CORE STYLES - OPTIMIZED
   Wszystkie niezbędne style w jednym pliku
   ============================================= */

/* ===== CSS VARIABLES ===== */
:root {
  /* Brand Colors */
  --sanctuary: #FDFCF8;
  --charcoal: #3A3A3A;
  --charcoal-light: #8A8A8A;
  --enterprise-brown: #8B7355;
  --sage: #8B9A8C;
  --ash: #E8E6E2;
  --terra: #B8956A;
  --temple-gold: #D4AF37;
  --pearl: rgba(253, 252, 248, 0.8);
  
  /* Typography */
  --font-cormorant: 'Cormorant Garamond', serif;
  --font-inter: 'Inter', sans-serif;
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: auto; /* Disable smooth scroll for better performance */
}

body {
  font-family: var(--font-inter);
  background-color: var(--sanctuary);
  color: var(--charcoal);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== HERO STYLES ===== */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding-top: 120px;
}

.hero-bg {
  position: absolute;
  inset: 0;
  z-index: 0;
  background-image: url('/images/background/bali-hero.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-color: var(--sanctuary);
}

.hero-gradient-overlay {
  position: absolute;
  inset: 0;
  z-index: 10;
  background: linear-gradient(
    180deg,
    rgba(252,246,238,0.4) 0%,
    rgba(255,255,255,0.7) 60%,
    rgba(255,255,255,0.9) 100%
  );
}

.hero-content {
  position: relative;
  z-index: 20;
  text-align: center;
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.hero-badge-wrapper {
  margin-bottom: 1.5rem;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease-out 0.2s forwards;
}

.hero-badge {
  display: inline-block;
  font-size: 11px;
  color: var(--enterprise-brown);
  letter-spacing: 3.5px;
  text-transform: uppercase;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  padding: 8px 16px;
  border: 1px solid rgba(139, 115, 85, 0.2);
}

.hero-title {
  font-family: var(--font-cormorant);
  font-weight: 200;
  color: var(--charcoal);
  line-height: 0.95;
  margin-bottom: 1rem;
  font-size: clamp(100px, 15vw, 200px);
  letter-spacing: 0.22em;
  margin-top: -10px;
  text-shadow: 0 0 40px rgba(44, 41, 40, 0.03);
  opacity: 0;
  transform: translateY(50px);
  animation: fadeInUp 1.2s ease-out 0.4s forwards;
}

.hero-subtitle {
  font-family: var(--font-cormorant);
  font-size: 21px;
  color: var(--enterprise-brown);
  font-style: italic;
  font-weight: 300;
  opacity: 0;
  margin-bottom: 2rem;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out 0.6s forwards;
}

.hero-description {
  font-size: 17px;
  color: var(--charcoal-light);
  max-width: 620px;
  margin: 0 auto 4rem auto;
  line-height: 1.85;
  font-weight: 400;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out 0.8s forwards;
}

/* ===== HERO STATS ===== */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem 5rem;
  max-width: 64rem;
  margin: 0 auto 4rem auto;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out 1.0s forwards;
}

@media (min-width: 768px) {
  .hero-stats {
    grid-template-columns: repeat(4, 1fr);
  }
}

.hero-stat {
  text-align: center;
}

.hero-stat-number {
  font-size: 42px;
  font-weight: 100;
  color: var(--enterprise-brown);
  margin-bottom: 0.5rem;
}

.hero-stat-label {
  font-size: 10px;
  color: var(--sage);
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 500;
}

/* ===== HERO BUTTONS ===== */
.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out 1.4s forwards;
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
    gap: 1.5rem;
  }
}

.hero-button {
  display: inline-flex;
  align-items: center;
  padding: 16px 48px;
  font-size: 13px;
  font-weight: 300;
  letter-spacing: 2px;
  text-transform: uppercase;
  border: 1px solid var(--enterprise-brown);
  color: var(--enterprise-brown);
  background: transparent;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.hero-button:hover {
  background: var(--enterprise-brown);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
}

.hero-button:focus {
  outline: none;
  opacity: 0.7;
}

.hero-button--whatsapp {
  border-color: var(--enterprise-brown);
  color: var(--enterprise-brown);
}

.hero-button--whatsapp:hover {
  background: var(--enterprise-brown);
  color: white;
}

/* ===== HERO SIDE FORM ===== */
.hero-side-form {
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  background: var(--pearl);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  padding: 2rem;
  max-width: 20rem;
  width: 320px;
  border: 1px solid rgba(193, 155, 104, 0.1);
  box-shadow: 0 10px 40px rgba(0,0,0,0.06);
  opacity: 0;
  transform: translateY(-50%) translateX(50px);
  animation: fadeInLeft 1.0s ease-out 1.6s forwards;
}

@media (max-width: 1279px) {
  .hero-side-form {
    display: none;
  }
}

.hero-side-form h3 {
  font-family: var(--font-cormorant);
  font-size: 1.25rem;
  color: var(--charcoal);
  margin-bottom: 0.5rem;
}

.hero-side-form p {
  font-size: 0.875rem;
  color: var(--sage);
  margin-bottom: 1.5rem;
}

.hero-side-form input {
  width: 100%;
  padding: 0.75rem 0;
  border: 0;
  border-bottom: 1px solid var(--ash);
  background: transparent;
  font-size: 0.875rem;
  transition: border-color 0.3s ease;
  margin-bottom: 1.5rem;
}

.hero-side-form input:focus {
  outline: none;
  border-bottom-color: var(--enterprise-brown);
}

.hero-side-form .form-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background: var(--enterprise-brown);
  color: white;
  font-weight: 300;
  letter-spacing: 2px;
  font-size: 13px;
  text-transform: uppercase;
  border: none;
  transition: all 0.3s ease;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-side-form .form-button:hover {
  background: var(--terra);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
}

.hero-side-form .form-button:focus {
  outline: none;
  opacity: 0.8;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* ===== RESPONSIVE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
  .hero-section {
    padding-top: 80px;
  }
  
  .hero-title {
    font-size: clamp(80px, 12vw, 120px);
    letter-spacing: 0.15em;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .hero-description {
    font-size: 16px;
    margin-bottom: 3rem;
  }
  
  .hero-stats {
    gap: 1.5rem 2rem;
  }
  
  .hero-stat-number {
    font-size: 32px;
  }
  
  .hero-buttons {
    gap: 1rem;
  }
  
  .hero-button {
    padding: 14px 32px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: clamp(60px, 10vw, 80px);
    letter-spacing: 0.1em;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
  
  .hero-description {
    font-size: 15px;
  }
  
  .hero-stat-number {
    font-size: 28px;
  }
  
  .hero-button {
    padding: 12px 24px;
    width: 100%;
    justify-content: center;
  }
}

/* ===== ACCESSIBILITY & MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  .hero-badge-wrapper,
  .hero-title,
  .hero-subtitle,
  .hero-description,
  .hero-stats,
  .hero-buttons,
  .hero-side-form {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .hero-side-form {
    transform: translateY(-50%);
  }
  
  .hero-button {
    transition: none;
  }
}

/* ===== SKIP LINKS ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: top 300ms ease;
}

.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}