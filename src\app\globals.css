@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import core optimized styles */
@import url('../styles/core.css');

/* ===== WHATSAPP BUTTON ===== */
.whatsapp-elegant {
  background-color: #8B7355;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.whatsapp-elegant:hover {
  background-color: rgba(139, 115, 85, 0.9);
  transform: scale(1.05);
}

.whatsapp-float {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
  z-index: 998;
}

@media (max-width: 768px) {
  .whatsapp-float {
    bottom: 1.5rem;
    right: 1.5rem;
  }
}

.whatsapp-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}