'use client';

import { useState, useEffect, useRef } from 'react';

// Statystyki do wyświetlenia w hero
const heroStats = [
  { number: '200h', label: 'YTT Certyfikacja' },
  { number: '7+', label: '<PERSON>t Doświadcz<PERSON>' },
  { number: '150+', label: 'Uczestników' },
  { number: '4.9', label: 'Średnia Ocena' }
];

const MinimalistHero = () => {
  const heroRef = useRef(null);
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) return null;
  
  return (
    <section 
      ref={heroRef}
      className="hero-section"
    >
      {/* Tło */}
      <div className="hero-bg" />
      
      {/* Gradient overlay */}
      <div className="hero-gradient-overlay" />
      
      {/* Główna zawartość */}
      <div className="hero-content">
        {/* Badge */}
        <div className="hero-badge-wrapper">
          <span className="hero-badge">
            RETREATY JOGI • BALI & SRI LANKA
          </span>
        </div>
        
        {/* Tytuł */}
        <h1 className="hero-title">
          BAKASANA
        </h1>
        
        {/* Podtytuł */}
        <p className="hero-subtitle">
          ~ jóga jest drogą ciszy ~
        </p>
        
        {/* Opis */}
        <p className="hero-description">
          Odkryj transformującą moc jogi w duchowych sercach Azji. Nasze retreaty to połączenie 
          tradycyjnej praktyki z luksusowym komfortem, tworząc przestrzeń dla głębokiej przemiany 
          w otoczeniu niesamowitych krajobrazów Bali i Sri Lanki.
        </p>
        
        {/* Statystyki */}
        <div className="hero-stats">
          {heroStats.map((stat, index) => (
            <div key={index} className="hero-stat">
              <div className="hero-stat-number">{stat.number}</div>
              <div className="hero-stat-label">{stat.label}</div>
            </div>
          ))}
        </div>
        
        {/* Przyciski CTA */}
        <div className="hero-buttons">
          <a href="/harmonogram" className="hero-button">
            Harmonogram
          </a>
          
          <a href="/kontakt" className="hero-button hero-button--whatsapp">
            Rezerwacja
          </a>
        </div>
      </div>
      
      {/* Formularz boczny (tylko desktop) */}
      <div className="hero-side-form">
        <h3>Zarezerwuj Konsultację</h3>
        <p>Zostaw swój email, a skontaktujemy się z Tobą w ciągu 24 godzin.</p>
        
        <form>
          <input type="email" placeholder="Twój email" required />
          <button type="submit" className="form-button">Kontakt</button>
        </form>
      </div>
    </section>
  );
};

export default MinimalistHero;